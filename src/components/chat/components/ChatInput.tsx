import React, { useState, useRef, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ChatFeatures, ChatTheme, getMaxMessageLength, isFileAllowed } from '../config/roleConfigurations';
import { useChat } from '@/hooks/useChat';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { 
  Send, 
  Paperclip, 
  Mic, 
  MicOff, 
  Smile, 
  X,
  File,
  Image,
  Video
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ChatInputProps {
  chatId?: string;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  onMessageSent?: (message: any) => void;
  replyToMessage?: any;
  onCancelReply?: () => void;
}

/**
 * ChatInput Component
 * 
 * Handles message composition with role-based features like file upload,
 * voice messages, emoji picker, and form validation.
 */
export const ChatInput: React.FC<ChatInputProps> = ({
  chatId,
  userRole,
  features,
  theme,
  onMessageSent,
  replyToMessage,
  onCancelReply,
}) => {
  const { sendMessage } = useChat();
  const { toast } = useToast();
  
  // Local state
  const [isRecording, setIsRecording] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Create message schema based on role features
  const messageSchema = z.object({
    message: z
      .string()
      .min(1, 'Message cannot be empty')
      .max(getMaxMessageLength(userRole), `Message too long (max ${getMaxMessageLength(userRole)} characters)`),
    attachments: z.array(z.instanceof(File)).optional(),
  });

  type MessageFormData = z.infer<typeof messageSchema>;

  // Form setup
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      message: '',
      attachments: [],
    },
  });

  const messageValue = watch('message');

  // Handle form submission
  const onSubmit = async (data: MessageFormData) => {
    if (!chatId) {
      toast({
        title: 'Error',
        description: 'No chat selected',
        variant: 'destructive',
      });
      return;
    }

    try {
      const messageData = {
        type: 'text' as const,
        message: data.message.trim(),
        ...(replyToMessage && { reply_to: replyToMessage.id }),
      };

      await sendMessage(chatId, messageData);
      
      // Reset form
      reset();
      setAttachedFiles([]);
      
      // Cancel reply if active
      if (onCancelReply) {
        onCancelReply();
      }
      
      // Callback
      if (onMessageSent) {
        onMessageSent(messageData);
      }
      
      // Focus back to textarea
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files || !features.hasFileUpload) return;

    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file) => {
      const validation = isFileAllowed(userRole, file.type, file.size);
      if (validation.allowed) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.reason}`);
      }
    });

    if (errors.length > 0) {
      toast({
        title: 'File Upload Error',
        description: errors.join('\n'),
        variant: 'destructive',
      });
    }

    if (validFiles.length > 0) {
      setAttachedFiles(prev => [...prev, ...validFiles]);
      setValue('attachments', [...attachedFiles, ...validFiles]);
    }
  }, [userRole, features.hasFileUpload, attachedFiles, setValue, toast]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (features.hasFileUpload) {
      setIsDragOver(true);
    }
  }, [features.hasFileUpload]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (features.hasFileUpload) {
      handleFileSelect(e.dataTransfer.files);
    }
  }, [features.hasFileUpload, handleFileSelect]);

  // Remove attached file
  const removeAttachedFile = (index: number) => {
    const newFiles = attachedFiles.filter((_, i) => i !== index);
    setAttachedFiles(newFiles);
    setValue('attachments', newFiles);
  };

  // Handle voice recording (placeholder)
  const toggleRecording = () => {
    if (!features.hasVoiceMessages) return;
    
    setIsRecording(!isRecording);
    // TODO: Implement actual voice recording
    toast({
      title: 'Voice Messages',
      description: 'Voice message feature coming soon!',
    });
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(onSubmit)();
    }
  };

  // Render file attachments
  const renderAttachments = () => {
    if (attachedFiles.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-2 p-2 border-t border-[var(--chat-border)]">
        {attachedFiles.map((file, index) => (
          <div
            key={index}
            className="flex items-center space-x-2 bg-[var(--chat-surface)] rounded-lg p-2 border border-[var(--chat-border)]"
          >
            <div className="flex items-center space-x-2">
              {file.type.startsWith('image/') ? (
                <Image className="h-4 w-4 text-[var(--chat-primary)]" />
              ) : file.type.startsWith('video/') ? (
                <Video className="h-4 w-4 text-[var(--chat-primary)]" />
              ) : (
                <File className="h-4 w-4 text-[var(--chat-primary)]" />
              )}
              <span className="text-sm text-[var(--chat-text)] truncate max-w-[120px]">
                {file.name}
              </span>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0"
              onClick={() => removeAttachedFile(index)}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>
    );
  };

  // Render reply preview
  const renderReplyPreview = () => {
    if (!replyToMessage) return null;

    return (
      <div className="flex items-center justify-between p-2 bg-[var(--chat-surface)] border-t border-[var(--chat-border)]">
        <div className="flex-1 min-w-0">
          <div className="text-xs text-[var(--chat-text-secondary)] mb-1">
            Replying to {replyToMessage.user.name}
          </div>
          <div className="text-sm text-[var(--chat-text)] truncate">
            {replyToMessage.message}
          </div>
        </div>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 flex-shrink-0"
          onClick={onCancelReply}
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    );
  };

  return (
    <div className="chat-input border-t border-[var(--chat-border)] bg-[var(--chat-background)]">
      {renderReplyPreview()}
      {renderAttachments()}
      
      <form onSubmit={handleSubmit(onSubmit)} className="p-4">
        <div
          className={cn(
            'relative flex items-end space-x-2 rounded-lg border border-[var(--chat-border)] bg-[var(--chat-surface)] p-2',
            isDragOver && 'border-[var(--chat-primary)] bg-[var(--chat-primary)]/5',
            errors.message && 'border-red-500'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {/* File upload button */}
          {features.hasFileUpload && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 flex-shrink-0"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Paperclip className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Attach file</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Message input */}
          <Textarea
            {...register('message')}
            ref={textareaRef}
            placeholder="Type a message..."
            className="flex-1 min-h-[40px] max-h-[120px] resize-none border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
            onKeyDown={handleKeyDown}
            disabled={isSubmitting}
          />

          {/* Voice message button */}
          {features.hasVoiceMessages && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className={cn(
                      'h-8 w-8 p-0 flex-shrink-0',
                      isRecording && 'text-red-500'
                    )}
                    onClick={toggleRecording}
                  >
                    {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isRecording ? 'Stop recording' : 'Voice message'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Send button */}
          <Button
            type="submit"
            size="sm"
            className="h-8 w-8 p-0 flex-shrink-0"
            disabled={isSubmitting || !messageValue.trim() || !chatId}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        {/* Error message */}
        {errors.message && (
          <p className="text-sm text-red-500 mt-1">{errors.message.message}</p>
        )}

        {/* Character count */}
        <div className="flex justify-between items-center mt-1 text-xs text-[var(--chat-text-secondary)]">
          <span>
            {messageValue.length}/{getMaxMessageLength(userRole)}
          </span>
          {isDragOver && (
            <span className="text-[var(--chat-primary)]">
              Drop files here to attach
            </span>
          )}
        </div>
      </form>

      {/* Hidden file input */}
      {features.hasFileUpload && (
        <Input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={(e) => handleFileSelect(e.target.files)}
          accept={features.allowedFileTypes.join(',')}
        />
      )}
    </div>
  );
};

export default ChatInput;

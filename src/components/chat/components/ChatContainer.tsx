import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useChat } from '@/hooks/useChat';
import { ChatFeatures, ChatTheme, ChatVariantConfig } from '../config/roleConfigurations';
import { ChatHeader } from './ChatHeader';
import { ChatMessageList } from './ChatMessageList';
import { ChatInput } from './ChatInput';
import { ChatParticipants } from './ChatParticipants';
import { ChatSettings } from './ChatSettings';
import { ChatSidebar } from './ChatSidebar';
import { cn } from '@/lib/utils';

interface ChatContainerProps {
  userRole: 'admin' | 'provider' | 'customer';
  userId: string;
  recipientId?: string;
  chatId?: string;
  variant: 'compact' | 'full' | 'modal';
  features: ChatFeatures;
  theme: ChatTheme;
  variantConfig: ChatVariantConfig;
  onChatSelect: (chatId: string) => void;
  onMessageSent: (message: any) => void;
}

/**
 * ChatContainer Component
 * 
 * Main container that orchestrates all chat components and handles
 * navigation, state management, and role-specific rendering.
 */
export const ChatContainer: React.FC<ChatContainerProps> = ({
  userRole,
  userId,
  recipientId,
  chatId: propChatId,
  variant,
  features,
  theme,
  variantConfig,
  onChatSelect,
  onMessageSent,
}) => {
  const navigate = useNavigate();
  const { chatId: routeChatId } = useParams<{ chatId?: string }>();
  const { state, loadChats, joinChat, leaveChat, connect } = useChat();
  
  // Determine active chat ID from props or route
  const activeChatId = propChatId || routeChatId;
  
  // Local state for UI
  const [showParticipants, setShowParticipants] = useState(
    variantConfig.showParticipants && variant === 'full'
  );
  const [showSettings, setShowSettings] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);

  // Check for mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobileView(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Initialize chat connection and load chats
  useEffect(() => {
    connect();
    loadChats();
  }, [connect, loadChats]);

  // Handle chat selection from route or props
  useEffect(() => {
    if (activeChatId && activeChatId !== state.selectedChat?.id) {
      joinChat(activeChatId);
      onChatSelect(activeChatId);
    }
  }, [activeChatId, state.selectedChat?.id, joinChat, onChatSelect]);

  // Handle recipient-based chat creation
  useEffect(() => {
    if (recipientId && !activeChatId) {
      // Find existing chat with this recipient or create new one
      const existingChat = state.activeChats.find(chat => 
        chat.participants.some(p => p.id === recipientId)
      );
      
      if (existingChat) {
        handleChatSelect(existingChat.id);
      } else {
        // TODO: Create new chat with recipient
        console.log('Creating new chat with recipient:', recipientId);
      }
    }
  }, [recipientId, activeChatId, state.activeChats]);

  // Handle chat selection
  const handleChatSelect = (selectedChatId: string) => {
    if (selectedChatId !== state.selectedChat?.id) {
      // Leave current chat if any
      if (state.selectedChat) {
        leaveChat();
      }
      
      // Join new chat
      joinChat(selectedChatId);
      onChatSelect(selectedChatId);
      
      // Update URL for full variant
      if (variant === 'full') {
        navigate(`/${userRole}/chat/${selectedChatId}`);
      }
    }
  };

  // Handle new chat creation
  const handleCreateNewChat = () => {
    if (variant === 'full') {
      navigate(`/${userRole}/chat/new`);
    }
    // TODO: Handle new chat creation for other variants
  };

  // Handle message sent
  const handleMessageSent = (message: any) => {
    onMessageSent(message);
  };

  // Toggle participants panel
  const handleToggleParticipants = () => {
    if (features.canManageParticipants) {
      setShowParticipants(!showParticipants);
    }
  };

  // Toggle settings panel
  const handleToggleSettings = () => {
    setShowSettings(!showSettings);
  };

  // Render based on variant
  const renderContent = () => {
    if (variant === 'compact') {
      return (
        <div className="flex flex-col h-full">
          {variantConfig.showHeader && (
            <ChatHeader
              chat={state.selectedChat}
              userRole={userRole}
              features={features}
              theme={theme}
              variant={variant}
              onToggleParticipants={handleToggleParticipants}
              onToggleSettings={handleToggleSettings}
            />
          )}
          
          <div className="flex-1 flex flex-col min-h-0">
            <ChatMessageList
              messages={state.messages}
              currentUserId={userId}
              userRole={userRole}
              features={features}
              theme={theme}
              isLoading={state.isLoading}
            />
            
            <ChatInput
              chatId={state.selectedChat?.id}
              userRole={userRole}
              features={features}
              theme={theme}
              onMessageSent={handleMessageSent}
            />
          </div>
        </div>
      );
    }

    if (variant === 'modal') {
      return (
        <div className="flex flex-col h-full">
          <ChatHeader
            chat={state.selectedChat}
            userRole={userRole}
            features={features}
            theme={theme}
            variant={variant}
            onToggleParticipants={handleToggleParticipants}
            onToggleSettings={handleToggleSettings}
            onClose={() => {
              // Handle modal close
              if (state.selectedChat) {
                leaveChat();
              }
            }}
          />
          
          <div className="flex-1 flex min-h-0">
            <div className="flex-1 flex flex-col">
              <ChatMessageList
                messages={state.messages}
                currentUserId={userId}
                userRole={userRole}
                features={features}
                theme={theme}
                isLoading={state.isLoading}
              />
              
              <ChatInput
                chatId={state.selectedChat?.id}
                userRole={userRole}
                features={features}
                theme={theme}
                onMessageSent={handleMessageSent}
              />
            </div>
            
            {showParticipants && (
              <ChatParticipants
                chat={state.selectedChat}
                userRole={userRole}
                features={features}
                theme={theme}
                onClose={() => setShowParticipants(false)}
              />
            )}
          </div>
          
          {showSettings && (
            <ChatSettings
              chat={state.selectedChat}
              userRole={userRole}
              features={features}
              theme={theme}
              onClose={() => setShowSettings(false)}
            />
          )}
        </div>
      );
    }

    // Full variant
    return (
      <div className="flex h-full">
        <ChatSidebar
          chats={state.activeChats}
          selectedChatId={state.selectedChat?.id}
          userRole={userRole}
          features={features}
          theme={theme}
          isLoading={state.isLoading}
          onChatSelect={handleChatSelect}
          onCreateNewChat={handleCreateNewChat}
          isMobile={isMobileView}
        />
        
        <div className="flex-1 flex flex-col min-w-0">
          {state.selectedChat ? (
            <>
              <ChatHeader
                chat={state.selectedChat}
                userRole={userRole}
                features={features}
                theme={theme}
                variant={variant}
                onToggleParticipants={handleToggleParticipants}
                onToggleSettings={handleToggleSettings}
              />
              
              <div className="flex-1 flex min-h-0">
                <div className="flex-1 flex flex-col">
                  <ChatMessageList
                    messages={state.messages}
                    currentUserId={userId}
                    userRole={userRole}
                    features={features}
                    theme={theme}
                    isLoading={state.isLoading}
                  />
                  
                  <ChatInput
                    chatId={state.selectedChat.id}
                    userRole={userRole}
                    features={features}
                    theme={theme}
                    onMessageSent={handleMessageSent}
                  />
                </div>
                
                {showParticipants && (
                  <ChatParticipants
                    chat={state.selectedChat}
                    userRole={userRole}
                    features={features}
                    theme={theme}
                    onClose={() => setShowParticipants(false)}
                  />
                )}
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-[var(--chat-text-secondary)]">
                <h3 className="text-lg font-medium mb-2">No chat selected</h3>
                <p>Select a conversation to start messaging</p>
              </div>
            </div>
          )}
        </div>
        
        {showSettings && (
          <ChatSettings
            chat={state.selectedChat}
            userRole={userRole}
            features={features}
            theme={theme}
            onClose={() => setShowSettings(false)}
          />
        )}
      </div>
    );
  };

  return (
    <div 
      className={cn(
        'chat-container',
        'h-full w-full',
        'bg-[var(--chat-background)]',
        'text-[var(--chat-text)]'
      )}
      data-testid="chat-container"
    >
      {renderContent()}
    </div>
  );
};

export default ChatContainer;

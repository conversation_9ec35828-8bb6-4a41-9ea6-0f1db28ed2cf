# Universal Chat Component

A comprehensive, reusable chat system for JobON that adapts to different user roles (admin, provider, customer) with role-specific features, permissions, and theming.

## Features

- **Role-Based Permissions**: Different features and capabilities based on user role
- **Multiple Variants**: Compact, full, and modal layouts
- **Customizable Themes**: Role-specific themes with custom override support
- **Real-time Messaging**: WebSocket integration for live updates
- **File Sharing**: Role-based file upload with type and size restrictions
- **TanStack Query Integration**: Optimized data fetching and caching
- **React Hook Form**: Form validation and submission
- **Responsive Design**: Works on desktop and mobile devices
- **Accessibility**: ARIA labels and keyboard navigation support

## Quick Start

```tsx
import { UniversalChat } from '@/components/chat';

// Basic usage
<UniversalChat
  userRole="customer"
  userId="user-123"
  variant="full"
/>
```

## Role Configurations

### Admin Role
- Full access to all features
- Can create and manage groups
- Can delete any messages
- Can access chat history
- File upload up to 50MB
- All file types allowed

### Provider Role
- Can upload files and send voice messages
- Cannot create groups
- Can only delete own messages
- File upload up to 25MB
- Limited file types (images, PDFs, videos, text)

### Customer Role
- Basic messaging features
- Cannot send voice messages
- Cannot edit messages
- File upload up to 10MB
- Limited file types (images, PDFs, text)

## Variants

### Full Variant
```tsx
<UniversalChat
  userRole="admin"
  userId="admin-123"
  variant="full"
  theme="admin"
/>
```
- Complete chat interface with sidebar
- Participant management
- Settings panel
- Best for dedicated chat pages

### Compact Variant
```tsx
<UniversalChat
  userRole="customer"
  userId="customer-123"
  variant="compact"
  className="w-80 h-96"
/>
```
- Minimal interface
- No sidebar or participants panel
- Perfect for widgets or embedded chat

### Modal Variant
```tsx
<UniversalChat
  userRole="provider"
  userId="provider-123"
  variant="modal"
  chatId="chat-456"
/>
```
- Popup-style interface
- Includes close button
- Great for overlay conversations

## Theming

### Built-in Themes
```tsx
// Use role-specific theme
<UniversalChat theme="admin" />
<UniversalChat theme="provider" />
<UniversalChat theme="customer" />

// Use default theme (based on user role)
<UniversalChat theme="default" />
```

### Custom Themes
```tsx
const customTheme = {
  primary: '#6366f1',
  secondary: '#64748b',
  accent: '#f59e0b',
  background: '#ffffff',
  surface: '#f8fafc',
  text: '#1e293b',
  // ... other theme properties
};

<UniversalChat
  theme="custom"
  customTheme={customTheme}
/>
```

## Feature Overrides

```tsx
<UniversalChat
  userRole="provider"
  features={{
    hasVideoCall: true, // Enable video calls
    maxFileSize: 100,   // Increase file size limit
    canCreateGroups: true, // Allow group creation
  }}
/>
```

## Event Handlers

```tsx
<UniversalChat
  onChatSelect={(chatId) => {
    console.log('Chat selected:', chatId);
  }}
  onMessageSent={(message) => {
    console.log('Message sent:', message);
  }}
  onError={(error) => {
    console.error('Chat error:', error);
  }}
/>
```

## Integration Examples

### Admin Dashboard
```tsx
import { AdminDashboardChat } from '@/components/chat/examples/ChatExamples';

export const AdminPage = () => (
  <div className="h-screen">
    <AdminDashboardChat />
  </div>
);
```

### Provider Dashboard
```tsx
import { ProviderDashboardChat } from '@/components/chat/examples/ChatExamples';

export const ProviderPage = () => (
  <div className="h-screen">
    <ProviderDashboardChat customerId="customer-123" />
  </div>
);
```

### Customer Support Widget
```tsx
import { CompactChatWidget } from '@/components/chat/examples/ChatExamples';

export const SupportWidget = () => (
  <CompactChatWidget
    userRole="customer"
    userId="customer-123"
    recipientId="support-agent"
  />
);
```

## Hooks

### useChatPermissions
```tsx
import { useChatPermissions } from '@/hooks/useChatPermissions';

const { permissions, canPerformAction, checkFilePermission } = useChatPermissions('admin');

if (canPerformAction('canDeleteMessages')) {
  // Show delete button
}
```

### useChatQueries
```tsx
import { useChatList, useSendMessage } from '@/hooks/useChatQueries';

const { data: chats, isLoading } = useChatList('user-123', 'customer');
const sendMessage = useSendMessage();
```

## File Structure

```
src/components/chat/
├── UniversalChat.tsx           # Main component
├── components/
│   ├── ChatContainer.tsx       # Main container
│   ├── ChatHeader.tsx          # Header with actions
│   ├── ChatMessageList.tsx     # Message display
│   ├── ChatInput.tsx           # Message input
│   ├── ChatSidebar.tsx         # Chat list sidebar
│   ├── ChatParticipants.tsx    # Participant management
│   └── ChatSettings.tsx        # Settings panel
├── config/
│   └── roleConfigurations.ts   # Role-based configs
├── examples/
│   └── ChatExamples.tsx        # Usage examples
└── index.ts                    # Exports
```

## Dependencies

- React 18.3.1+
- React Router DOM 6.26.2+
- TanStack Query 5.56.2+
- React Hook Form 7.54.2+
- Radix UI components
- Tailwind CSS
- Zod for validation

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure accessibility compliance

## License

MIT License - see LICENSE file for details
